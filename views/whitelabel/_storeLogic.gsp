<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "url": "${request.getRequestURL()}",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://${request.getServerName()+storeUrl}?searchString={search_term_string}",
    "query-input": "required name=search_term_string"
  }
}
</script>
<%if(publisherDescription!=null){%>
<script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "${publisherName}",
            "url": "${request.getRequestURL()}",
            "description": "${publisherDescription}"
          }
    </script>
<%}%>

<%
    String logoUrl = "https://www.wonderslate.com/assets/wsLandingpage/ws-logo.png";

    if("27".equals(""+session["siteId"])){
        logoUrl = "https://prepjoy.com/assets/prepJoy/prepjoy.svg";
    }
%>

<!-- Organization Schema -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Wonderslate",
  "url": "${request.getRequestURL()}",
  "logo": "${logoUrl}",
  "sameAs": [
    "https://www.facebook.com/profile.php?id=61561621995625",
    "https://www.instagram.com/gptsir_/",
    "https://www.youtube.com/channel/UC-87LRCX4mo9jmPCqG3S9ZQ"
  ]
}
</script>

<!-- Breadcrumb Schema -->
<script>
    (function() {
        // Base breadcrumbs
        const breadcrumbs = [
            { name: "Home", url: "${request.getRequestURL()}" }
        ];

        const path = window.location.pathname.split("/").filter(Boolean);

        // Detect store page
        if (path[0] === "store") {
            breadcrumbs.push({ name: "Store", url: "${request.getRequestURL()}" });

            // Detect subcategory (if any)
            if (path.length > 1) {
                // Convert URL segment to readable name
                const categoryName = path[1].replace(/-/g, " ").replace(/\b\w/g, l => l.toUpperCase());
                breadcrumbs.push({ name: categoryName, url: window.location.href });
            }

            // Detect product page (if URL includes 'ebook-details')
            if (window.location.href.includes("ebook-details")) {
                const productTitleElement = document.querySelector(".book-title");
                if (productTitleElement) {
                    breadcrumbs.push({ name: productTitleElement.innerText.trim(), url: window.location.href });
                }
            }
        }

        // Build JSON-LD List
        const itemListElement = breadcrumbs.map((crumb, index) => ({
            "@type": "ListItem",
            "position": index + 1,
            "name": crumb.name,
            "item": crumb.url
        }));

        const breadcrumbSchema = {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": itemListElement
        };

        // Inject JSON-LD into page
        const script = document.createElement("script");
        script.type = "application/ld+json";
        script.text = JSON.stringify(breadcrumbSchema);
        document.head.appendChild(script);
    })();
</script>

<!-- ItemList Schema (Products) -->
<script type="application/ld+json" id="itemlist-schema">
{
  "@context": "https://schema.org",
  "@type": "ItemList",
  "itemListElement": []
}
</script>

<script>
    // Function to update ItemList schema based on displayed books
    function updateItemListSchema() {
        const bookCards = document.querySelectorAll('.topSchoolBooks');
        const items = [];

        bookCards.forEach((bookCard, index) => {
            try {
                const titleElement = bookCard.querySelector('.book-title p, .left-div p:first-child');
                const linkElement = bookCard.querySelector('a');
                const imageElement = bookCard.querySelector('img');
                const priceElement = bookCard.querySelector('.price');
                const originalPriceElement = bookCard.querySelector('.price span');
                const publisherElement = bookCard.querySelector('.book-publisher-name');

                if (titleElement && linkElement) {
                    const title = titleElement.innerText.trim();
                    const url = linkElement.href;
                    const image = imageElement ? imageElement.src : '';

                    // Extract price - handle both ₹ symbol and FREE badge
                    let price = '';
                    let originalPrice = '';
                    if (priceElement) {
                        const priceText = priceElement.innerText || priceElement.textContent;
                        if (priceText.includes('FREE')) {
                            price = '0';
                        } else {
                            price = priceText.replace(/[^0-9.]/g, '');
                        }
                    }

                    if (originalPriceElement) {
                        originalPrice = originalPriceElement.innerText.replace(/[^0-9.]/g, '');
                    }

                    const publisher = publisherElement ? publisherElement.innerText.replace('By ', '').trim() : '';

                    const item = {
                        "@type": "ListItem",
                        "position": index + 1,
                        "url": url,
                        "name": title,
                        "image": image
                    };

                    // Add offer information if price is available
                    if (price) {
                        item.offers = {
                            "@type": "Offer",
                            "price": price,
                            "priceCurrency": "INR"
                        };

                        // Add high price if original price exists
                        if (originalPrice && originalPrice !== price) {
                            item.offers.highPrice = originalPrice;
                        }
                    }

                    // Add publisher if available
                    if (publisher) {
                        item.brand = {
                            "@type": "Brand",
                            "name": publisher
                        };
                    }

                    items.push(item);
                }
            } catch (error) {
                console.log('Error processing book card:', error);
            }
        });

        // Update the schema script
        const schemaScript = document.getElementById('itemlist-schema');
        if (schemaScript && items.length > 0) {
            schemaScript.textContent = JSON.stringify({
                "@context": "https://schema.org",
                "@type": "ItemList",
                "itemListElement": items
            }, null, 2);
        }
    }

    // Call the function when books are loaded
    // This will be called from the displayBooks function
    window.updateItemListSchema = updateItemListSchema;
</script>

<script>

    <%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())
      %>

    function shareBooks(){
        var booksUrl = "https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.href);
        openShareContentModal("ebooks", booksUrl);
    }
</script>
<script>
    <%
        String requestUrl = ""+request.getRequestURL()
        String queryString =  request.getQueryString()%>
    <%    if(requestUrl.indexOf("wonderslate.com")==-1&&requestUrl.indexOf("prepjoy.com")==-1&&requestUrl.indexOf("/sp/")>-1&&queryString==null){
    %>
    var base_url = location.protocol + '//' + location.host;
    window.history.replaceState("", "", base_url);
    <%}%>
    var tokenId="${params.tokenId}";
    var allBooksData;
    var useSearchSelect = false;
    var pageNo;
    var isAjaxCalled;
    var allBooksLoaded = false;
    var showPublishers = false;
    var searchMode = false;
    var searchStatus = "";
    var pageInitialized = false;
    var siteName="";
    var prepjoySite = "${session['prepjoySite']}";
    var subscriptionBooks=false;
    var amazonSearch = false;
    var wiley=false;
    var wileyCollectionBookId=-1;
    <%if("66".equals(""+session["siteId"])){%>
    wiley=true;
    <%}%>
    <%if(session['wileyCollectionBookId']!=null){%>
    wileyCollectionBookId = ${session['wileyCollectionBookId']};
    <%}%>

    var ebookOfTheDay,newlyReleasedEbook,trendingNowEbook,urltags = "";

    var disablePrev = 1;

    <%if("true".equals(session["prepjoySite"]) || "true".equals(session["commonWhiteLabel"])){%>
    siteName="${session['siteName']}";
    <%}else{%>
    siteName="${session['entryController']}";
    <% }%>

    <%if(showPublishers!=null&&"true".equals(showPublishers)){%>
        showPublishers = true;
   <% }%>



    function submitSearchTop(){
        isAjaxCalled = true;
        var searchString = $(searchInputSelector).val();
        var callRemoteSearch = true;

        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }

        document.getElementById("content-data-books-ebooks").innerHTML="";
        if(searchStringValues!=null&&searchStringValues!=""){

            //use the filters
            for(var i=0;i<searchStringValues.length;i++){
                if(searchStringValues[i]==searchString){
                    callRemoteSearch = populateFromFilters(searchDataValues[i]);
                    break
                }
            }
        }
        if(callRemoteSearch) {
            var level=null,syllabus=null,grade=null,publisherId=null,subject=null,pageNo;
           <% if(params.publisherId!=null||params.level!=null||params.syllabus!=null||params.grade!=null||params.subject!=null) {%>
            pageInitialize();

            <%}else {%>

                <g:remoteFunction controller="discover" action="search"  onSuccess='booksReceived(data);'
         params="'searchString='+searchString" />
            <%}%>
        }

    }

    function populateFromFilters(searchData){
        var level=null,syllabus=null,grade=null,publisherId=null,subject=null,pageNo;
        if(searchData.hasOwnProperty("publisherId")||searchData.hasOwnProperty("level")||searchData.hasOwnProperty("syllabus")||searchData.hasOwnProperty("grade")||searchData.hasOwnProperty("subject")){
            if(searchData.hasOwnProperty("publisherId")){
                publisherId = searchData.publisherId;
            }
            if(searchData.hasOwnProperty("level")){
                level = searchData.level;
            }
            if(searchData.hasOwnProperty("syllabus")){
                syllabus = searchData.syllabus;
            }
            if(searchData.hasOwnProperty("grade")){
                grade = searchData.grade;
            }
            if(searchData.hasOwnProperty("subject")){
                subject = searchData.subject;
            }
            setBlankState();
            getBooksWithParameters(level,syllabus,grade,subject,publisherId,0);
        }
        else{
            return true
        }

    }

    function replaceAll(str, find, replace) {
        if(str==undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }
    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }



    function booksReceived(data){
        searchStatus = "";
        if(data.status=="Nothing present"){
            $("#load-more-button").css('display','none');
            searchStatus = data.status;
            // $("#noResultsFound").show();
            // $("#level").focus();


            if (!prepjoySite){
                $('.loading-icon').addClass('hidden');
            } else {
                $('#loading').hide();
            }
            //to get top level filters and publishers for filters
            searchMode=true;
            pageNo = -1;
            allBooksLoaded = true;
            setBlankState();
            getBooks();

            $("#resetFilter").removeAttr('disabled').css('color','#007bff');
            var searchStringNotFound=$(searchInputSelector).val();
          if ("${session['siteId']}" == 27 || "${session['siteId']}" == 1 ){
              $("#amazonBooksTitle").html('').hide();
              amazonSearch = true;
            <g:remoteFunction controller="affiliation" action="amazonSearch"  onSuccess='amazonSearchResults(data);'
                  params="'searchKey='+searchStringNotFound" />
          }

        }else {
            //reset the filters here
            document.getElementById("content-data-books-ebooks").innerHTML = "";
            allBooksData = data;
            var books = JSON.parse(JSON.stringify(data.books));
            if(data.booksTag.length>0){
                var bookTag = data.booksTag[0];
                setBlankState();
                displayBooks(books);
            }
            else {

                displayBooks(books);
                //to get top level filters and publishers for filters
                searchMode=true;
                pageNo = -1;
                allBooksLoaded = true;
            }
            $("#resetFilter").removeAttr('disabled').css('color','#007bff');
        }

    }
    function  amazonSearchResults(data){
        $("#resetFilter").removeAttr('disabled').css('color','#007bff');
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        } else {
            $('#loading').hide();
        }
     var books = JSON.parse(JSON.stringify(data.results));
        var imgSrc="";
        var ebookHtmlStr = "";
        var listPrice;
        var offerPrice;
        var rating;
        var starTotal = 5;
        var starPercentage;
      for(var i=0;i<books.length;i++){
          imgSrc = books[i].ImageUrl;
          listPrice =  books[i].ListPrice;
          offerPrice = books[i].Price;
          starPercentage = (books[i].Rating / starTotal) * 100;
          rating = Math.round(starPercentage / 5) * 5;
          ebookHtmlStr = ebookHtmlStr + "<div class='col-12 d-flex d-sm-block justify-content-start mb-2 bg-white p-2 p-md-3 fadein-animated'>" +
              "<a href='" +books[i].DetailPageURL+ "?tag=wonderslate-21&linkCode=w13' target='_blank' class='topSchoolBooks'>" +
              "<div class='image-wrapper'>";
          ebookHtmlStr += "<img src='" + imgSrc + "' alt='Book Cover Image' width='175' height='220'/>" ;
          ebookHtmlStr +="</div>";
          ebookHtmlStr += "<div class='content-wrapper col px-0'>" +
              "<div class='left-div'>" +
              "<p class='mb-0' style='color: #212121 !important;font-size: 16px'>"+ books[i].Title + "</p>";
          ebookHtmlStr +=  "<p class='book-publisher-name mb-0'>By " + books[i].Subtitle + "</p><p class='mb-1 text-black-50'>ASIN : "+ books[i].ASIN +"</p>" +
              "</div>";
          ebookHtmlStr += "<div class='right-div'>";
          if(books[i].Rating != null && books[i].Rating != "") {
              ebookHtmlStr += "<div class='stars-outer'>" +
                  "<div class='stars-inner' style='width: "+rating+"%;'></div>"+
                  "</div><span class='ratings'> "+ books[i].Rating + " (" + books[i].TotalReviews + " reviews)</span>";
          }
          ebookHtmlStr +="<p class='price' style='font-size: 18px !important;line-height: inherit;'>";
          if(listPrice) {
              ebookHtmlStr += "<span class='mr-2 ml-0' style='color:#ee3539; font-weight: 400;font-size: 16px !important;'>" + listPrice + "</span>";
          }
          ebookHtmlStr += offerPrice;
          ebookHtmlStr += "</p>";
          ebookHtmlStr += "</div>";
          ebookHtmlStr += "</div>" +
              "</a></div>";
      }
        if(books.length>0) {
            document.getElementById("content-data-books-ebooks").innerHTML = ebookHtmlStr;
            $("#content-data-books-ebooks").show();
            $("#amazonBooksTitle").html('<h5 class="mb-0">Books from Amazon and Flipkart</h5>').show();
            $("#noResultsFound").hide();
        } else {
            $("#noResultsFound").show();
            $("#amazonBooksTitle").html('').hide();
        }
    }
    function displayBooks(books){
       // $("#noResultsFound").hide();
        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
        var noOfEbooks  = 0;
        var ebookHtmlStr="";
        var imgSrc = "";

        var bookUrl="";

        var offerPrice = "";
        var listPrice = "";
        var addToCartBtn = "";

        var bookArray = [];
        for(var i=0; i<books.length;i++){
            if(bookArray.includes(books[i].id)) continue;
            else bookArray.push(books[i].id);
            if (books[i].offerPrice || (books[i].testsPrice && books[i].offerPrice)){
                if (books[i].offerPrice == 0 || books[i].offerPrice == 0.0 || books[i].offerPrice == null) {
                    offerPrice = "<small style='font-weight: 500;'>FREE</small>";
                    addToCartBtn = "";
                } else {
                    offerPrice = "&#x20b9 " + books[i].offerPrice;
                    //Removed Add to Card button from the store page
                }
                if (books[i].listPrice == 0 || books[i].listPrice == 0.0 || books[i].listPrice == "null" || books[i].listPrice == null || books[i].listPrice == books[i].offerPrice) {
                    listPrice = "";
                } else {
                    listPrice = "&#x20b9 " + books[i].listPrice;
                }
            }else if(books[i].testsPrice){
                if (books[i].testsPrice == 0 || books[i].testsPrice == 0.0 || books[i].testsPrice == null) {
                    offerPrice = "<small style='font-weight: 500;'>FREE</small>";
                    addToCartBtn = "";
                } else {
                    offerPrice = "&#x20b9 " + books[i].testsPrice;
                    //Removed Add to Card button from the store page
                }
                if (books[i].testsListprice == 0 || books[i].testsListprice == 0.0 || books[i].testsListprice == "null" || books[i].testsListprice == null || books[i].testsListprice == books[i].testsPrice) {
                    listPrice = "";
                } else {
                    listPrice = "&#x20b9 " + books[i].testsListprice;
                }
            }else if(books[i].bookgptSellPrice){
                if (books[i].bookgptSellPrice == 0 || books[i].bookgptSellPrice == 0.0 || books[i].bookgptSellPrice == null) {
                    offerPrice = "<small style='font-weight: 500;'>FREE</small>";
                    addToCartBtn = "";
                } else {
                    offerPrice = "&#x20b9 " + books[i].bookgptSellPrice;
                    //Removed Add to Card button from the store page
                }
                if (books[i].bookgptListPrice == 0 || books[i].bookgptListPrice == 0.0 || books[i].bookgptListPrice == "null" || books[i].bookgptListPrice == null || books[i].bookgptListPrice == books[i].bookgptSellPrice) {
                    listPrice = "";
                } else {
                    listPrice = "&#x20b9 " + books[i].bookgptListPrice;
                }
            }

            imgSrc = books[i].coverImage;
            if (books[i].coverImage!=null && books[i].coverImage.startsWith("https")) {
                imgSrc = books[i].coverImage;
                imgSrc = imgSrc.replace("~", ":");
            } else {
                imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=passport";
            }



            if (books[i].listPrice !=0 && books[i].listPrice !=null ){
                var offerPricePer = books[i].offerPrice;
                var actualPricePer = books[i].listPrice;
                var percentageVal = (actualPricePer - offerPricePer);
                percentageVal =(percentageVal * 100 / actualPricePer).toFixed(0);
            }else if (books[i].bookgptListPrice !=0 && books[i].bookgptListPrice !=null ){
                var offerPricePer = books[i].bookgptSellPrice;
                var actualPricePer = books[i].bookgptListPrice;
                var percentageVal = (actualPricePer - offerPricePer);
                percentageVal =(percentageVal * 100 / actualPricePer).toFixed(0);
            }
            ebookHtmlStr = ebookHtmlStr + "<div class='col-12 d-flex d-sm-block justify-content-start mb-2 bg-white p-2 p-md-3 fadein-animated shadow'>" +
                "<div class='topSchoolBooks'>" +
                "<div class='image-wrapper'>";
            if(books[i].testTypeBook=="true") {
                ebookHtmlStr += "<a href='/wpmain/aiBookDtl?bookId=" + books[i].id +"'>"

            }else{
            <% if(session['appInApp']==null){%>
                ebookHtmlStr += "<a href='/" + replaceAll(books[i].title,' ','-') +"/ebook-details?siteName="+siteName+"&bookId=" + books[i].id +"&publisher="+replaceAll(books[i].publisher,' ','-')+"&preview=true' target='_blank'>"
           <%  }else{%>
                ebookHtmlStr += "<a href='/" + replaceAll(books[i].title,' ','-')+"/ebook-details?siteName="+siteName+"&bookId=" + books[i].id + "&preview=true'>"
           <% }%>
            }
            ebookHtmlStr += "<div class='bookShadow'>";
            if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                let isIbookGPTPublisher = false;
                if(books[i].publisher == "iBookGPT"){
                    isIbookGPTPublisher = true;
                }
                ebookHtmlStr += createCover(books[i].grade, books[i].title, books[i].subject, books[i].level, books[i].syllabus, isIbookGPTPublisher);
            }
            else {
                ebookHtmlStr += "<img src='" + imgSrc + "' alt='Book Cover Image' width='175' height='220'/>" ;
            }

            ebookHtmlStr +="</div>";

            ebookHtmlStr +="</a>" +
                "</div>";

            ebookHtmlStr += "<div class='content-wrapper col px-0'>" +
                "<div class='left-div'>";
                if(books[i].testTypeBook=="true") {
                    ebookHtmlStr += "<a href='/wpmain/aiBookDtl?bookId=" + books[i].id +"' target='_blank'>"

                }
               else if(tokenId=="") {
                    ebookHtmlStr += "<a href='/" + replaceAll(books[i].title,' ','-') + "/ebook-details?siteName="+siteName+"&bookId=" + books[i].id +"&publisher="+replaceAll(books[i].publisher,' ','-')+"&preview=true' target='_blank' class='book-title'>"
                }else{
                    ebookHtmlStr += "<a href='/" + replaceAll(books[i].title,' ','-') +"/ebook-details?siteName="+siteName+"&bookId=" + books[i].id + "&preview=true&tokenId=" + tokenId + "' class='book-title'>"
                }
                ebookHtmlStr += "<p class='mb-0' style='color: #212121 !important;font-size: 16px'>"+ books[i].title + "</p>" +
                "</a>";
            ebookHtmlStr +=  "<p class='book-publisher-name mb-0'>By " + books[i].publisher + "</p>";
            if (books[i].isbn != null && books[i].isbn != "") {
                ebookHtmlStr += "<p class='mb-1 text-black-50'>ISBN : "+books[i].isbn+"</p>";
            }
                ebookHtmlStr +="</div>";
            if (books[i].bookType == "print"||subscriptionBooks||(wiley&&wileyCollectionBookId!=books[i].id)) {
                ebookHtmlStr +="";
            } else {
                ebookHtmlStr += "<div class='right-div'>";
                if ((books[i].listPrice > books[i].offerPrice) && percentageVal > 0 ){
                    ebookHtmlStr += "<div class='badge-overlay'>"+
                        "<span class='badge'>"+percentageVal+"% OFF</span>"+
                        "</div>";
                }else if((books[i].bookgptListPrice > books[i].bookgptSellPrice) && percentageVal > 0){
                    ebookHtmlStr += "<div class='badge-overlay'>"+
                        "<span class='badge'>"+percentageVal+"% OFF</span>"+
                        "</div>";
                }
                ebookHtmlStr +="<p class='price' style='font-size: 18px !important;line-height: inherit;margin-bottom: 15px;'>";
                    if(listPrice) {
                        ebookHtmlStr += "<span class='mr-2 ml-0' style='color:#ee3539; font-weight: 400;font-size: 16px !important;'>" + listPrice + "</span>";
                    }
                    ebookHtmlStr += offerPrice;
                    if (books[i].validityDays == "365") {
                        ebookHtmlStr += "<small style='font-size: 13px;color: #949494;'> for 1 year</small>";
                    }
                    ebookHtmlStr += "</p>";
                if(books[i].bookType === "bookgpt" || books[i].bookType ==="ebookwithai"){
                    if(books[i].testTypeBook=="true")
                        ebookHtmlStr +="<a href='/wpmain/aibook?siteName="+siteName+"&bookId="+books[i].id+"' style='padding: 5px 10px;background: #F79420;color: #fff;border-radius: 5px;'>Try for Free</a>"
                    else
                    ebookHtmlStr +="<a href='/prompt/bookgpt?siteName="+siteName+"&bookId="+books[i].id+"' style='padding: 5px 10px;background: #F79420;color: #fff;border-radius: 5px;'>Try for Free</a>"
                }else if(books[i].printOnly == false){
                    ebookHtmlStr +="<a href='/" + replaceAll(books[i].title,' ','-') + "/ebook?siteName="+siteName+"&bookId=" + books[i].id+"&preview=true' style='padding: 5px 10px;background: #F79420;color: #fff;border-radius: 5px;'>Try for Free</a>"
                }
                ebookHtmlStr += "</div>";
            }
            ebookHtmlStr += "</div>" +
                "</div>" +

                "</div>";
            document.getElementById("content-data-books-ebooks").innerHTML += ebookHtmlStr;
            ebookHtmlStr="";
            noOfEbooks++;
        }


        if(noOfEbooks>0)   {
            if(searchMode){
                $("#load-more-button").css('display','none');
                searchMode = false;
                if(searchStatus=="Nothing present"){
                    if ("${session['siteId']}" == 27 || "${session['siteId']}" == 1 ) {
                        if(!amazonSearch){
                            $("#amazonBooksTitle").html('').hide();
                            $("#noResultsFound").hide();
                        }
                        amazonSearch = false;
                    } else {
                        $("#noResultsFound").show();
                    }
                    $("#load-more-button").css('display','none');
                    // if (prepjoySite){
                    //     $('#content-data-books-ebooks').removeClass('row').hide();
                    //     $('#searchResults').hide();
                    // }
                }
            }
            else{
                $("#amazonBooksTitle").html('').hide();
                $("#noResultsFound").hide();
                if(noOfEbooks>=20)   {
                    $("#load-more-button").css('display','block');
                    $('#view-more').text('Show More');
                } else {
                    subscriptionBooks=false;
                    $("#load-more-button").css('display','none');
                }
            }

        } else {
            ebookHtmlStr=document.getElementById("content-data-books-ebooks").innerHTML;
            $("#load-more-button").css('display','none');
            //$("#syllabus,#grade,#subject").removeClass("background-bg");
            ebookHtmlStr +="<div class='w-100 mb-4 text-center col-12' style='margin-top: -50px;'>" +
                "<span class=\"material-icons text-primary text-primary-modifier\" style='font-size: 50px;font-weight: normal;'>\n" +
                "mood_bad\n" +
                "</span>" +
                "<p class='mb-2' style='color: #212121;font-size: 16px'>No results found.</p>" +
                "<p class='text-secondary text-secondary-modifier'>We can't find any eBooks matching your search.</p>" +
                "</div>";
            document.getElementById("content-data-books-ebooks").innerHTML = ebookHtmlStr;

        }


        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else {
            $('#loading').hide();
        }

        var fronts = document.querySelectorAll(".uncover");
        for(var i=0 ; i < fronts.length; i++) {
            fronts[i].style.background = colors[i%11];
        }

        $(".app_in_app #content-data-books-ebooks a").on('click', function () {

            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else {
                $('#loading').show();
            }

        });

        // Update ItemList schema after books are displayed
        if (typeof updateItemListSchema === 'function') {
            updateItemListSchema();
        }

    }

    pageNo = 0;
    isAjaxCalled = false;
    $(window).load(function () {
        $("#view-more").click(function() {
            $('#view-more').text('Loading..');
            // if ($(window).scrollTop() >= $(document).height() - $(window).height() - 200 && !isAjaxCalled && !allBooksLoaded) {
                getBooks();
            // }
        });


    });

    var prepJoySearchBook=false;
    var catalogueLevel=null, catalogueSyllabus=null, catalogueGrade=null, catalogueSubject=null, cataloguePublisher=null;
    function getBooksWithParameters(level,syllabus,grade,subject,publisherId,pageNo){
        level =replaceAll(level,'-',' ');
        syllabus = replaceAll(syllabus,'-',' ');
        grade = replaceAll(grade,'-',' ');
        subject = replaceAll(subject,'-',' ');



       if (!prepjoySite){
           $('.loading-icon').removeClass('hidden');
       }else{
           $('#loading').show();
       }

        var title="";
        var pageUrl = window.location.href;
        if (pageUrl.indexOf("?") > 0) {
            pageUrl = pageUrl.substr(0, pageUrl.indexOf("?"));
        }
        pageUrl+="?linkSource=store&";

        if(level!=null&&level!=""&&level!="null"){
            catalogueLevel = level;
            pageUrl+="level="+replaceAll(decodeURI(level),' ','-')+"&";
            $("#resetFilter").removeAttr('disabled').css('color','#007bff');
        }
        if(syllabus!=null&&syllabus!=""&&syllabus!="null"){
            catalogueSyllabus = syllabus;
            pageUrl+="syllabus="+replaceAll(decodeURI(syllabus),' ','-')+"&";
            title +=syllabus+" "
        }
        if(grade!=null&&grade!=""&&grade!="null"&&grade!=""){
            catalogueGrade = grade;
            pageUrl+="grade="+replaceAll(decodeURI(grade),' ','-')+"&";
            title +=grade+" "
        }
        if(subject!=null&&subject!=""&&subject!="null"){
            catalogueSubject = subject;
            pageUrl+="subject="+replaceAll(decodeURI(subject),' ','-')+"&";
        }

        if(publisherId!=null&&publisherId!=""&&publisherId!="null"){
            $("#resetFilter").removeAttr('disabled').css('color','#007bff');
            cataloguePublisher = publisherId;
            if(showPublishers&&document.getElementById("publisher").selectedIndex>0){

                    var publisherName = document.getElementById("publisher")[document.getElementById("publisher").selectedIndex].label;
                    publisherName = publisherName.split(" ").join("-");
                    pageUrl += "publisher=" + publisherName

            }
            else{
                pageUrl+="publisherId="+publisherId;
            }
        }

       if(pageInitialized) window.history.replaceState(title + " eBooks on - Wonderslate", "title", pageUrl);
       else pageInitialized = true;

        <g:remoteFunction controller="wonderpublish" action="getNewBooksList"  onSuccess='latestBooksReceived(data);'
                  params="'fromApp=false&categories=true&level='+level+'&syllabus='+syllabus+'&grade='+grade+'&subject='+subject+'&pageNo='+pageNo+'&publisherId='+publisherId+'&getSubscription='+subscriptionBooks" />
    }
    function getBooks(){
        pageNo = pageNo+1;
        isAjaxCalled = true;
        var level = encodeURIComponent(document.getElementById("level")[document.getElementById("level").selectedIndex].value);
        var syllabus = encodeURIComponent(document.getElementById("syllabus")[document.getElementById("syllabus").selectedIndex].value);
        var grade = encodeURIComponent(document.getElementById("grade")[document.getElementById("grade").selectedIndex].value);
        var subject = encodeURIComponent(document.getElementById("subject")[document.getElementById("subject").selectedIndex].value);
        var publisherId = null;
        console.log("level="+level);
        var element =  document.getElementById('publisher');
        if (showPublishers)
        {
            publisherId = encodeURIComponent(document.getElementById("publisher")[document.getElementById("publisher").selectedIndex].value);
        }else{
            <%if(session["publisherLogo"]!=null){%>
            publisherId = ${session["publisherLogo"]};
            <%}%>
        }

        getBooksWithParameters(level,syllabus,grade,subject,publisherId,pageNo);
    }



    function compareNumbers(a, b) {
        return a - b;
    }
    function latestBooksReceived(data) {

        var books = JSON.parse(data.books);

        var booksPresent = true;

        if(books == '' || books == null || books == 'null' || data.books == '[]') booksPresent = false;

        if("institute"=="${params.source}"&&books.length==0){
            getBooksWithParameters(null,null,null,null,null,0);
        }else {

            if (booksPresent) {
                $('#filters, .searching-book-store').removeClass('d-none').addClass('d-flex');

                var levelSelect = document.getElementById("level");
                var syllabusSelect = document.getElementById("syllabus");
                var gradeSelect = document.getElementById("grade");
                var subjectSelect = document.getElementById("subject");
                //initialise

                <%if(!"true".equals(session["doNotChangeLabel"])){%>
                syllabusSelect.options[0].innerHTML = "Select Board";
                gradeSelect.options[0].innerHTML = "Select Grade";
                subjectSelect.options[0].innerHTML = "Select Subject";
                <%}%>

                var checkLevel = false, checkSyllabus = false, checkGrade = false;

                var levelTags = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
                var syllabusTags = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
                var gradeTags = [];
                var subjectTags = [];


                var tagsList = JSON.parse(data.bookTags);
                if (data.publisherId != null && "undefined" == data.publisherId) {


                }
                var levels = [];
                if (!(data.publisherId == ""||data.publisherId=="null")) {
                    levelTags = JSON.parse(data.bookTags);
                    syllabusTags = JSON.parse(data.bookTags);
                }
                gradeTags = JSON.parse(data.bookTags);

                subjectTags = JSON.parse(data.bookTags);

                for (var i = 0; i < levelTags.length; i++) {
                    if (levels.indexOf(levelTags[i].level) == -1) levels.push(levelTags[i].level);
                }
                levels.sort();

                //reset level
                var select;
                select = document.getElementById("level");
                select.options.length = 1;
                for (var i = 0; i < levels.length; i++) {
                    var el = document.createElement("option");
                    el.textContent = levels[i];
                    el.value = levels[i];
                    if (data.level!=null&&levels[i] == data.level.replace("&amp;","&")) {
                        el.selected = true;
                        $("#level").addClass("background-bg");
                        $("#syllabus").show();
                        // $("#syllabus").focus();
                        pageNo = 0;
                        allBooksLoaded = false;
                      //  $('html, body').animate({scrollTop: $('#filters').offset().top - 77}, 'slow');
                    }
                    select.appendChild(el);
                }

                var selectedLevel = document.getElementById("level")[document.getElementById("level").selectedIndex].value;

                if (document.getElementById("level").selectedIndex > 0) checkLevel = true


                var syllabus = [];
                for (var i = 0; i < syllabusTags.length; i++) {
                    if (checkLevel) {
                        if (selectedLevel == syllabusTags[i].level && syllabus.indexOf(syllabusTags[i].syllabus) == -1) syllabus.push(syllabusTags[i].syllabus);
                    } else if (syllabus.indexOf(syllabusTags[i].syllabus) == -1) {
                        syllabus.push(syllabusTags[i].syllabus);
                    }
                }
                syllabus.sort();


                var select;
                select = document.getElementById("syllabus");
                select.options.length = 1;
                for (var i = 0; i < syllabus.length; i++) {
                    var el = document.createElement("option");
                    el.textContent = syllabus[i];
                    el.value = syllabus[i];
                    if (data.syllabus!=null&&syllabus[i] == data.syllabus.replace("&amp;","&")) {
                        el.selected = true;
                        $("#syllabus").addClass("background-bg");
                        $("#grade").show();
                        // $("#grade").focus();
                        pageNo = 0;
                        allBooksLoaded = false;
                    }
                    select.appendChild(el);
                }

                var selectedSyllabus = document.getElementById("syllabus")[document.getElementById("syllabus").selectedIndex].value;
                if (document.getElementById("syllabus").selectedIndex > 0) checkSyllabus = true


                //reset grade

                //  $("#subject").hide();
                if(gradeTags.length>0) {
                    var grade = [];
                    for (var i = 0; i < gradeTags.length; i++) {
                        //if level is selected
                        if (grade.indexOf(gradeTags[i].grade) == -1) {
                            grade.push(gradeTags[i].grade);
                        }

                    }

                    if (data.level == 'School') {
                        grade.sort(compareNumbers);
                    } else {
                        grade.sort();
                    }


                var select;
                select = document.getElementById("grade");
                select.options.length = 1;
                for (var i = 0; i < grade.length; i++) {
                    var el = document.createElement("option");
                    el.textContent = grade[i];
                    el.value = grade[i];
                    if (grade[i] == data.grade) {
                        el.selected = true;
                        $("#grade").addClass("background-bg");
                        $("#subject").show();
                        // $("#subject").focus();

                    }
                    select.appendChild(el);
                }
                    <%if(!session['wileySite'] ){%> $("#grade").show();<%}%>
                }else{
                    $("#grade").hide();
                }

                if(subjectTags.length>0&&subjectTags[0].subject!='') {
                    var selectedGrade = document.getElementById("grade")[document.getElementById("grade").selectedIndex].value;

                    if (document.getElementById("grade").selectedIndex > 0) checkGrade = true
                    //reset subject

                    var subject = [];
                    for (var i = 0; i < subjectTags.length; i++) {
                       if(subject.indexOf(subjectTags[i].subject) == -1) subject.push(subjectTags[i].subject);
                    }
                    subject.sort();
                    var select;
                    select = document.getElementById("subject");
                    select.options.length = 1;
                    for (var i = 0; i < subject.length; i++) {
                        var el = document.createElement("option");
                        el.textContent = subject[i];
                        el.value = subject[i];
                        if (subject[i] == data.subject) {
                            el.selected = true;
                            $("#subject").addClass("background-bg");
                        }
                        select.appendChild(el);
                    }
                    <%if(!session['wileySite'] ){%> $("#subject").show();<%}%>
                }else{
                    $("#subject").hide();
                }
                //change the publisher thingy only of the pageNumber and when publisher is not selected

                if (pageNo == 0 && showPublishers) {
                    var publishers = JSON.parse(data.publishers);
                    var select;
                    select = document.getElementById("publisher");
                    select.options.length = 1;

                    for (var i = 0; i < publishers.length; i++) {
                        var el = document.createElement("option");
                        el.textContent = publishers[i].publisher;
                        el.value = publishers[i].publisherId;
                        if (publishers[i].publisherId == data.publisherId) {
                            el.selected = true;
                            $("#publisher").addClass("background-bg");
                            allBooksLoaded = false;
                         //   $('html, body').animate({scrollTop: $('#filters').offset().top - 77}, 'slow');
                        }
                        select.appendChild(el);
                    }
                }

                // change the tag names if required
                <%if(!"true".equals(session["doNotChangeLabel"])){%>
                if (levelSelect.selectedIndex > 0) {
                    if ("Government Recruitments" == levelSelect[levelSelect.selectedIndex].value) {
                        syllabusSelect.options[0].innerHTML = "Select Level";
                        if ("National Level" == syllabusSelect[syllabusSelect.selectedIndex].value) {
                            gradeSelect.options[0].innerHTML = "Select Exam";
                        } else if ("State Level" == syllabusSelect[syllabusSelect.selectedIndex].value) {
                            gradeSelect.options[0].innerHTML = "Select State";
                            subjectSelect.options[0].innerHTML = "Select Exam";
                        }
                    } else if ("Engineering Entrances" == levelSelect[levelSelect.selectedIndex].value || "Medical Entrances" == levelSelect[levelSelect.selectedIndex].value) {
                        syllabusSelect.options[0].innerHTML = "Select Exam";
                        gradeSelect.options[0].innerHTML = "Select Exam";

                    } else if ("Competitive Exams" == levelSelect[levelSelect.selectedIndex].value) {
                        syllabusSelect.options[0].innerHTML = "Select Exam";
                        if ("University Entrances" == syllabusSelect[syllabusSelect.selectedIndex].value) {
                            gradeSelect.options[0].innerHTML = "Select University";
                        } else
                            gradeSelect.options[0].innerHTML = "Select Exam";
                    } else if ("College" == levelSelect[levelSelect.selectedIndex].value) {
                        syllabusSelect.options[0].innerHTML = "Select Course";
                        gradeSelect.options[0].innerHTML = "Select Semester";
                    } else if ("General" == levelSelect[levelSelect.selectedIndex].value) {
                        syllabusSelect.options[0].innerHTML = "Select Type";
                        gradeSelect.options[0].innerHTML = "Select Genre";
                    } else if ("Magazine" == levelSelect[levelSelect.selectedIndex].value) {
                        syllabusSelect.options[0].innerHTML = "Select Type";
                        if ("Current Affairs" == syllabusSelect[syllabusSelect.selectedIndex].value) {
                            gradeSelect.options[0].innerHTML = "Select Exam";
                        } else if ("Know Your State" == syllabusSelect[syllabusSelect.selectedIndex].value) {
                            gradeSelect.options[0].innerHTML = "Select State";
                        }
                    }
                }
<%}%>
                    isAjaxCalled = false;
                    displayBooks(books);

            }
            else{
                isAjaxCalled = true;
                allBooksLoaded = true;
                $("#load-more-button").css('display', 'none');

                if (!prepjoySite) {
                    $('.loading-icon').addClass('hidden');
                } else {
                    $('#loading').hide();
                }
                if (subscriptionBooks) {
                    document.getElementById("content-data-books-ebooks").innerHTML = "<p class='container px-4 px-md-5' style='color: #212121;font-size: 16px'>No books found.</p>";
                    subscriptionBooks = false;
                }
                document.getElementById("content-data-books-ebooks").innerHTML = "<div class='text-center noBooks'>" +
                    "<b>No books found.<br><br> Please clear all filters and try again." +
                    "<br>You can use Search to find the books you are looking for.</b><br>" +
                    "<button onclick='resetFilters()' id='resetFilterNew' class='btn mt-3' style='background: rgba(0,0,0,0.2);width: 125px;padding: 3px; border-radius: 4px;color:rgba(0,0,0,0.75);'>Clear All Filters</button>"+
                    "</div>";

                if (prepjoySite){
                    document.querySelector('.noBooks').classList.add('text-white');
                    document.querySelector('#resetFilterNew').classList.add('bg-white')
                }
            }
        }
    }


    const colorBank = ['#FF5722', '#2196F3', '#3F51B5', '#E91E63', '#009688', '#FF9800'];

    function truncate(text, maxLength) {
        if(text==null) return "";
        return text.length > maxLength ? text.slice(0, maxLength - 3) + '...' : text;
    }

    function createCover(grade, title, subject, level, syllabus, brand) {
        if(grade==null) grade="";
        if(title==null) title="";
        if(subject==null) subject="";
        if(level==null) level="";
        if(syllabus==null) syllabus="";
        const color = colorBank[Math.floor(Math.random() * colorBank.length)];

        let gradeText = grade
        if(level === "School"){
            gradeText = !grade.includes("Class") ? "Class "+grade : grade;
        }else if(level === "College"){
            gradeText = !grade.includes("Semester") ? "Semester "+grade : grade;
        }

        let subjectText = subject
        if(subjectText === "All" || subjectText === "All subjects"){
            subjectText = syllabus
        }

        let coverHtmlStr = "<div class='dummy_cover'>";
        if(brand){
            coverHtmlStr += "<div class='dummy_cover-section top' style='display: flex !important;align-items: center'>"
        }else {
            coverHtmlStr += "<div class='dummy_cover-section top'>";
        }
        coverHtmlStr += "<span>" + truncate(gradeText, 10) + "</span>";
        if(brand){
            coverHtmlStr +="<span class='dummy_cover_brand'>iBook<span class='gpt'>GPT</span></span>";
        }
        coverHtmlStr += "</div>" +
                "<div class='dummy_cover-section middle' style='background-color: " + color + ";'>" +
                    "<div class='dummy_cover_titleText'>" +truncate(title, 40) +
                    "</div>" +
                "</div>" +
                "<div class='dummy_cover-section bottom'>" +
                   truncate(subjectText, 15) +
                "</div>" +
            "</div>";
        return coverHtmlStr;
    }
</script>

<script>

    function setBlankState(){
        if(showPublishers) {
            document.getElementById("publisher").selectedIndex = 0;
        }
        document.getElementById("level").selectedIndex=0;
        document.getElementById("syllabus").selectedIndex=0;
        document.getElementById("grade").selectedIndex=0;
        document.getElementById("subject").selectedIndex=0;
        $("#level,#syllabus,#grade,#subject,#publisher").removeClass("background-bg");
       // $("#syllabus").hide();
      //  $("#grade").hide();
       // $("#subject").hide();

        subscriptionBooks = false;

        var syllabusSelect = document.getElementById("syllabus");
        var gradeSelect = document.getElementById("grade");
        var subjectSelect =  document.getElementById("subject");
        //initialise
       <%if(!"true".equals(session["doNotChangeLabel"])){%>
        syllabusSelect.options[0].innerHTML = "Select Board";
        gradeSelect.options[0].innerHTML = "Select Grade";
        subjectSelect.options[0].innerHTML = "Select Subject";
        <%}%>
    }


    function resetFilters(){
        <%if(session['appInApp']==null){%>
        $('#search-book-header, #search-book-store, #search-book-home').val("");
        <%}%>
        document.getElementById("content-data-books-ebooks").innerHTML="";
        pageNo=-1;
        allBooksLoaded=false;
        setBlankState();
        getBooks();
        catalogueLevel=null; catalogueSyllabus=null; catalogueGrade=null; catalogueSubject=null; cataloguePublisher=null;
        //holding categories on clear filter

        $("#resetFilter").removeAttr('disabled').css('color','#007bff');
        $(".app_in_app  .mdl-layout__header-row").removeClass("showing-mobile-search");
        $(".app_in_app .mdl-layout__header-row .mdl-navigation").removeClass("d-none");
        $("#load-more-button").css('display','none');
        <%if("books".equals(""+session["entryController"])){%>
        $("body").removeClass('showing-search-form');
        $(".search-close-icon,header .navbar-search").addClass("d-none");
        $(".search-open-icon").removeClass("d-none");
        <%}%>
        $("#amazonBooksTitle").html('').hide();
    }

    function levelChanged(field){
        allBooksLoaded=false;
        //first the level

     /**   var select;
        select = document.getElementById("syllabus");
        select.options.length = 1;
        select = document.getElementById("grade");
        select.options.length = 1;
        select = document.getElementById("subject");
        select.options.length = 1;*/

       // $("#syllabus,#grade,#subject").removeClass("background-bg");
        if(document.getElementById("level").selectedIndex>0) $("#level").addClass("background-bg");
        else $("#level").removeClass("background-bg");
        $("#syllabus").show();
     //   $("#grade").hide();
     //   $("#subject").hide();

        //Search field show/hide
        $(".searchShowHide").hide();
        $(".search-ebooks").show();

        //call get books
        pageNo = -1;
        document.getElementById("content-data-books-ebooks").innerHTML="";
        getBooks();

        $("#resetFilter").removeAttr('disabled').css('color','#007bff');
       // $('html, body').animate({scrollTop: $('#filters').offset().top - 77 }, 'slow');
        $("#load-more-button").css('display','none');
    }

    function syllabusChanged(field) {
        allBooksLoaded=false;
     //   $("#grade,#subject").removeClass("background-bg");
            //adding all selection
       /** var select;
        select = document.getElementById("grade");
        select.options.length = 1;
        select = document.getElementById("subject");
        select.options.length = 1;*/
     //   $("#subject").hide();
        if(document.getElementById("syllabus").selectedIndex>0) $("#syllabus").addClass("background-bg");
        else $("#syllabus").removeClass("background-bg");

        //Search field show/hide
        $(".searchShowHide").hide();
        $(".search-ebooks").show();

        $("#grade").show();
        document.getElementById("content-data-books-ebooks").innerHTML="";
        pageNo=-1;
        getBooks();
       // $('html, body').animate({scrollTop: $('#filters').offset().top - 77 }, 'slow');
        $("#load-more-button").css('display','none');
    }
    function gradeChanged(field){
        allBooksLoaded=false;
        isAjaxCalled = true;
        var select;

      if(document.getElementById("grade").selectedIndex>0) $("#grade").addClass("background-bg");
        else $("#grade").removeClass("background-bg");
        //Search field show/hide
        $(".searchShowHide").hide();
        $(".search-ebooks").show();

        pageNo = -1;
        document.getElementById("content-data-books-ebooks").innerHTML = "";
        getBooks();
        $("#subject").show();
     //   $('html, body').animate({scrollTop: $('#filters').offset().top - 77 }, 'slow');
        $("#load-more-button").css('display','none');
    }

    function subjectChanged(field){
        allBooksLoaded=false;
        isAjaxCalled = true;
        pageNo = -1;
        document.getElementById("content-data-books-ebooks").innerHTML = "";
        if(document.getElementById("subject").selectedIndex>0) $("#subject").addClass("background-bg");
        else $("#subject").removeClass("background-bg");
        getBooks();

        //Search field show/hide
        $(".searchShowHide").hide();
        $(".search-ebooks").show();
       // $('html, body').animate({scrollTop: $('#filters').offset().top - 77 }, 'slow');
        $("#load-more-button").css('display','none');
    }

    function publisherChanged(field){
        allBooksLoaded=false;
        isAjaxCalled = true;
        document.getElementById("content-data-books-ebooks").innerHTML = "";
        //call get books
        pageNo = -1;

        getBooks();
        if($(field).val()) $(field).addClass('background-bg');

        var publisherId = field[field.selectedIndex].value;
        var publisherName = field[field.selectedIndex].text;
        publisherName = publisherName.split(" ").join("-");

        //Search field show/hide
        $(".searchShowHide").hide();
        $(".search-ebooks").show();


        $("#resetFilter").removeAttr('disabled').css('color','#007bff');
    //    $('html, body').animate({scrollTop: $('#filters').offset().top - 77 }, 'slow');
        $("#load-more-button").css('display','none');
    }



    function pageInitialize(){
         var level=null,syllabus=null,grade=null,publisherId=null,subject=null,pageNo;
        //if anything coming from request
        <%if(params.level!=null){%>
          level =encodeURIComponent("${params.level.replace('~','&')}".replace('&amp;','&'));
        <%}else if(session.getAttribute('instituteLevel')!=null){%>
        level =encodeURIComponent("${session.getAttribute('instituteLevel').replace('~','&')}".replace('&amp;','&'));
        <%}%>
        <%if(params.syllabus!=null){%>
        syllabus = encodeURIComponent("${params.syllabus.replace('~','&')}".replace('&amp;','&'));
        <%}else if(params.level==null&&session.getAttribute('instituteSyllabus')!=null){%>
        syllabus =encodeURIComponent("${session.getAttribute('instituteSyllabus').replace('~','&')}".replace('&amp;','&'));
        <%}%>
        <%if(params.grade!=null){%>
        grade=encodeURIComponent("${params.grade.replace('~','&')}".replace('&amp;','&'));
        <%}%>
        <%if(params.subject!=null){%>
        subject=encodeURIComponent("${params.subject.replace('~','&')}".replace('&amp;','&'));
        <%}%>
        <%if(publisherId!=null){%>
        publisherId="${publisherId}";
        <%}%>

            pageNo = 0;
            isAjaxCalled = true;

           getBooksWithParameters(level,syllabus,grade,subject,publisherId,pageNo);
    }
    <%if(params.searchString!=null){%>
    $(searchInputSelector).val("${params.searchString}");
    submitSearchTop();
    $("#resetFilter").removeAttr('disabled').css('color','#007bff');
    <%}else{%>

    var data = { "books":"${booksList.get("books")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "publishers":"${booksList.get("publishers")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "bookTags":"${booksList.get("bookTags")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "level":"${booksList.get("level")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "syllabus":"${booksList.get("syllabus")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "grade":"${booksList.get("grade")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "subject":"${booksList.get("subject")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "publisherId":"${booksList.get("publisherId")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "featuredPublishers":"${booksList.get("featuredPublishers")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "status":"${booksList.get("books") ? "OK" : "Nothing present"}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&')
    };
    latestBooksReceived(data);
    <%}%>

    <%if("true".equals(session["prepjoySite"])){%>
        function loginOpen(){
            $('#prepjoySignupModal').modal('show');
        }
    <%}%>
</script>

<script>
    $('.search-ebooks').on('click',function () {
        $('.searchShowHide').css('display','flex');
        $(this).hide();
    });

    if (performance.navigation.type == 2) {
        window.location.replace(window.location.pathname);
    }

    function downloadCatalogue() {
        window.location.href = "/books/downloadCatalogue?categories=true&level="+catalogueLevel+"&syllabus="+catalogueSyllabus+"&grade="+catalogueGrade+"&subject="+catalogueSubject+"&publisherId="+cataloguePublisher;
    }

    function showSubscriptionEbooks(){
        subscriptionBooks=true;
        pageNo=-1;
        document.getElementById("content-data-books-ebooks").innerHTML="";
        getBooks();
    }

    console.log("the query string is ${request.getQueryString()}");
</script>



